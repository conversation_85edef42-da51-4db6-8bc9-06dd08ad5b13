package payment

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// CancelIntentParams contains the parameters for cancelling a payment intent
type CancelIntentParams struct {
	IntentID string
	ClientID string
}

// CancelIntentResponse contains the response data for a cancelled payment intent
type CancelIntentResponse struct {
	IntentID    string    `json:"payment_intent_id"`
	Status      string    `json:"status"`
	CancelledAt time.Time `json:"cancelled_at"`
}

// CancelIntent cancels an existing payment intent
func CancelIntent(ctx context.Context, params CancelIntentParams) (*CancelIntentResponse, *code.KGError) {
	ctx, span := tracing.Start(ctx, "payment.CancelIntent")
	defer span.End()

	// Validate input parameters
	if params.IntentID == "" {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("intent ID is required"), nil)
	}

	if params.ClientID == "" {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("client ID is required"), nil)
	}

	// Get the payment intent
	intent, err := r.GetPaymentIntentByID(ctx, params.IntentID)
	if err != nil {
		if errors.Is(err, domain.ErrRecordNotFound) {
			return nil, code.NewKGError(code.PaymentIntentNotFound, http.StatusNotFound, err, nil)
		}
		kglog.ErrorfCtx(ctx, "failed to get payment intent: %v", err)
		return nil, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	// Verify the client ID matches
	if intent.ClientID != params.ClientID {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid client id"), nil)
	}

	// Acquire lock to prevent concurrent modifications
	lockKey := fmt.Sprintf("payment:%s", intent.ID)
	blockTime := intent.PaymentChain.BlockTime()
	err = r.AcquireLock(ctx, lockKey, blockTime*10)
	if err != nil {
		if err != domain.ErrLockNotAcquired {
			kglog.ErrorfCtx(ctx, "[payment.CancelIntent] Failed to acquire lock for intent %s: %v", intent.ID, err)
		}
		return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}
	defer r.ReleaseLock(ctx, lockKey)

	// Re-fetch the intent to ensure we have the latest state after acquiring the lock
	intent, err = r.GetPaymentIntentByID(ctx, params.IntentID)
	if err != nil {
		kglog.ErrorfCtx(ctx, "failed to re-fetch payment intent after lock: %v", err)
		return nil, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	// Double-check that the intent can still be cancelled
	if !canBeCancelled(intent.Status) {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest,
			fmt.Errorf("payment intent with status '%s' cannot be cancelled", intent.Status), nil)
	}

	// Update the intent status to cancelled
	cancelledAt := time.Now()
	cancelledStatus := domain.PaymentIntentStatusCancelled

	intentUpdate := &domain.PaymentIntentUpdate{
		Status:      &cancelledStatus,
		CancelledAt: &cancelledAt,
	}

	err = r.UpdatePaymentIntent(ctx, intent.ID, intentUpdate)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "failed to update payment intent to cancelled", map[string]any{
			"error":     err.Error(),
			"intent_id": intent.ID,
		})
		return nil, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	// Update the intent object for callback
	intent.Status = cancelledStatus
	intent.CancelledAt = &cancelledAt

	// Send callback and notifications asynchronously
	IntentStatusChanged(ctx, intent)

	kglog.InfoWithDataCtx(ctx, "payment intent cancelled successfully", map[string]any{
		"intent_id":    intent.ID,
		"client_id":    intent.ClientID,
		"cancelled_at": cancelledAt,
	})

	return &CancelIntentResponse{
		IntentID:    intent.ID,
		Status:      string(cancelledStatus),
		CancelledAt: cancelledAt,
	}, nil
}

// canBeCancelled checks if a payment intent with the given status can be cancelled
func canBeCancelled(status domain.PaymentIntentStatus) bool {
	switch status {
	case domain.PaymentIntentStatusPending:
		return true
	case domain.PaymentIntentStatusSuccess,
		domain.PaymentIntentStatusExpired,
		domain.PaymentIntentStatusInsufficientRefunded,
		domain.PaymentIntentStatusInsufficientNotRefunded,
		domain.PaymentIntentStatusCancelled:
		return false
	default:
		return false
	}
}
