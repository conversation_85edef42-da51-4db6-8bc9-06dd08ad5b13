#!/bin/sh

if [ "$( container inspect -f '{{.State.Running}}' redis7-kg 2>/dev/null )" != "true" ]; then
		container images pull redis:7.0
		container run --name redis7-kg \
			--restart unless-stopped \
			-v redis7-data-kg:/data \
			-d \
			redis:7.0

	# Wait for Red<PERSON> to be ready
	Waiting_Msg="Waiting for Redis to be ready"
	SECONDS_WAITED=0
	TIMEOUT=30
	Dots=""
	while ! container exec redis7-kg -- redis-cli ping 2>/dev/null | grep -q "PONG"; do
		if [ "${SECONDS_WAITED}" -ge "${TIMEOUT}" ]; then
			echo "Timeout waiting for Red<PERSON> to become ready"
			exit 1
		fi
		Dots="$Dots."
		printf '\r%s' "${Waiting_Msg}${Dots}"
		sleep 1
		SECONDS_WAITED=$((SECONDS_WAITED + 1))
	done
	echo "OK"
fi
