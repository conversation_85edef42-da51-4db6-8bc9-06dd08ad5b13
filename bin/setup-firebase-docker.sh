#!/bin/sh

if [ "$( container inspect -f '{{.State.Running}}' firebase-kg 2>/dev/null )" != "true" ]; then
		# firebase-emulator only supports linux/amd64
		container images pull --platform=linux/amd64 kryptogodev/firebase-emulator:latest
		container run --platform=linux/amd64 --name firebase-kg \
		  --restart=unless-stopped \
			--env "GCP_PROJECT=testing-kryptogo-wallet-app" \
			--env "FIRESTORE_EMULATOR_PORT=8080" \
			--env "UI_EMULATOR_PORT=4000" \
			--env "AUTH_EMULATOR_PORT=9099" \
			--env "STORAGE_EMULATOR_PORT=9199" \
			--env "ENABLE_UI=true" \
			-d \
			kryptogodev/firebase-emulator:latest
fi
