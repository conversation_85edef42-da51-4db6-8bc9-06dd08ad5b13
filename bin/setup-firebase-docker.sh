#!/bin/sh

if [ "$( container inspect -f '{{.State.Running}}' firebase-kg 2>/dev/null )" != "true" ]; then
		# firebase-emulator only supports linux/amd64
		container images pull --platform=linux/amd64 kryptogodev/firebase-emulator:latest
		container run --platform=linux/amd64 --name firebase-kg \
		  --restart=unless-stopped \
			--env "GCP_PROJECT=testing-kryptogo-wallet-app" \
			--env "FIRESTORE_EMULATOR_PORT=8080" \
			--env "UI_EMULATOR_PORT=4000" \
			--env "AUTH_EMULATOR_PORT=9099" \
			--env "STORAGE_EMULATOR_PORT=9199" \
			--env "ENABLE_UI=true" \
			-d \
			kryptogodev/firebase-emulator:latest

	# Wait for Firebase emulator to be ready
	Waiting_Msg="Waiting for Firebase emulator to be ready"
	SECONDS_WAITED=0
	TIMEOUT=60
	Dots=""
	while ! container exec firebase-kg -- curl -s http://localhost:4000 >/dev/null 2>&1; do
		if [ "${SECONDS_WAITED}" -ge "${TIMEOUT}" ]; then
			echo "Timeout waiting for Firebase emulator to become ready"
			exit 1
		fi
		Dots="$Dots."
		printf '\r%s' "${Waiting_Msg}${Dots}"
		sleep 1
		SECONDS_WAITED=$((SECONDS_WAITED + 1))
	done
	echo "OK"
fi
