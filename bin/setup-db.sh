#!/bin/sh

if [ "$( container inspect -f '{{.State.Running}}' mysql8-kg 2>/dev/null )" != "true" ]; then
		container images pull mysql:8.0
		# Create data directory if it doesn't exist
		mkdir -p "$HOME/.kg-wallet/mysql-data"

		container run mysql:8.0 \
			-e MYSQL_ROOT_PASSWORD="root" -e MYSQL_DATABASE=wallet \
			-v "$HOME/.kg-wallet/mysql-data":/var/lib/mysql \

	Waiting_Msg="Waiting for MySQL to be ready"
	SECONDS_WAITED=0
	TIMEOUT=60
	Dots=""
	while ! container exec mysql8-kg -- mysqladmin ping --silent 2>/dev/null; do
		if [ "${SECONDS_WAITED}" -ge "${TIMEOUT}" ]; then
			echo "Timeout waiting for MySQL to become ready"
			exit 1
		fi
		Dots="$Dots."
		printf '\r%s' "${Waiting_Msg}${Dots}"
		sleep 1
		SECONDS_WAITED=$((SECONDS_WAITED + 1))
	done
	echo "OK"
fi
