#!/bin/sh

if [ "$( container inspect -f '{{.State.Running}}' mysql8-kg 2>/dev/null )" != "true" ]; then
		container images pull mysql:8.0
		container run --name mysql8-kg \
			-e MYSQL_ROOT_PASSWORD="$MYSQL_PASSWORD" -e MYSQL_DATABASE=wallet \
			-v mysql8-data-kg:/var/lib/mysql \
			-d \
			mysql:8.0

	Waiting_Msg="Waiting for MySQL to be ready"
	SECONDS_WAITED=0
	TIMEOUT=60
	Dots=""
	while ! container exec mysql8-kg -- mysqladmin ping --silent 2>/dev/null; do
		if [ "${SECONDS_WAITED}" -ge "${TIMEOUT}" ]; then
			echo "Timeout waiting for MySQL to become ready"
			exit 1
		fi
		Dots="$Dots."
		printf '\r%s' "${Waiting_Msg}${Dots}"
		sleep 1
		SECONDS_WAITED=$((SECONDS_WAITED + 1))
	done
	echo "OK"
fi
