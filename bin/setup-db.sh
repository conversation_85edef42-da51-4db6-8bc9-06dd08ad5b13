#!/bin/sh

if [ "$( container inspect -f '{{.State.Running}}' mysql8-kg 2>/dev/null )" != "true" ]; then
		container images pull mysql:8.0
		container run --name mysql8-kg \
			-e MYSQL_ROOT_PASSWORD="$MYSQL_PASSWORD" -e MYSQL_DATABASE=wallet \
			-v mysql8-data-kg:/var/lib/mysql \
      --health-cmd='mysqladmin ping --silent' \
			-d \
			mysql:8.0

	Waiting_Msg="Waiting for container to be healthy"
	SECONDS_WAITED=0
	TIMEOUT=60
	Dots=""
	while STATUS=$(container inspect mysql8-kg); [ "${STATUS}" != "healthy" ]; do
		if [ "${STATUS}" = "unhealthy" ]; then
			echo "Container is unhealthy"
			exit 1
		fi
		if [ "${SECONDS_WAITED}" -ge "${TIMEOUT}" ]; then
			echo "Timeout waiting for the container to become healthy"
			exit 1
		fi
		Dots="$Dots."
		printf '\r%s' "${Waiting_Msg}${Dots}"
		sleep 1
		SECONDS_WAITED=$((SECONDS_WAITED + 1))
	done
	echo "OK"
fi
