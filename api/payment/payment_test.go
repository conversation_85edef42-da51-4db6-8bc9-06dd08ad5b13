package payment

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/payment"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setupPaymentTest(t *testing.T) (*gin.Engine, string) {
	rdb.Reset()
	ctrl := gomock.NewController(t)
	r := repo.Unified()
	e := domain.NewMockCallbackExecutor(ctrl)
	oauth.Init(r)
	application.Init(r)
	tokenmeta.Init(repo.Unified(), []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	// Add token metadata for both USDT and USDC
	tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.Arbitrum, TokenID: "******************************************"}: {
			Name:     "Tether USD",
			Symbol:   "USDT",
			Decimals: 6,
		},
		{Chain: domain.Arbitrum, TokenID: "******************************************"}: {
			Name:     "USD Coin",
			Symbol:   "USDC",
			Decimals: 6,
		},
	})

	payment.Init(r, e, nil)

	e.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	mockCoingecko := coingeckoapi.NewMockICoingecko(ctrl)
	coingeckoapi.Set(mockCoingecko)

	// Mock SimplePrice with a more flexible implementation that handles all cases
	mockCoingecko.EXPECT().
		SimplePrice(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, ids []domain.CoingeckoID, vsCurrency string) (map[domain.CoingeckoID]float64, error) {
			result := make(map[domain.CoingeckoID]float64)

			// Handle empty array case (legacy params)
			if len(ids) == 0 {
				// We still need to return a non-zero value to satisfy the validation
				// Doesn't matter what the exact value is since it's not used with empty IDs
				// but need to be non-zero to avoid "invalid exchange rate" error
				return map[domain.CoingeckoID]float64{"default": 1.0}, nil
			}

			// Process each token ID in the request
			for _, id := range ids {
				// Always return a valid non-zero rate to avoid "invalid exchange rate" error
				switch id {
				case domain.CoingeckoID("tether"):
					switch strings.ToLower(vsCurrency) {
					case "usd":
						result[id] = 1.0
					case "twd":
						result[id] = 32.5
					default:
						// For any other currency, return a reasonable default value
						result[id] = 1.0
					}
				case domain.CoingeckoID("usd-coin"):
					switch strings.ToLower(vsCurrency) {
					case "usd":
						result[id] = 1.0
					case "twd":
						result[id] = 32.5
					default:
						// For any other currency, return a reasonable default value
						result[id] = 1.0
					}
				default:
					// For any other token, return a reasonable default
					result[id] = 1.0
				}
			}

			return result, nil
		}).AnyTimes()

	// Add mocks for IsCurrencySupported method
	mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "usd").Return(true, nil).AnyTimes()
	mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "twd").Return(true, nil).AnyTimes()
	mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "eur").Return(false, nil).AnyTimes()
	// Catch-all for any other currency after specific ones are checked
	mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

	mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
	mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
	alchemyapi.Set(mockAlchemy)

	// Create test organizations and OAuth applications
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "KryptoGO"},
		{ID: 2, Name: "Test App"},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.OAuthClientConfig{
		{
			ID:           "test_client_id",
			Domain:       "http://test.com",
			IsPrivileged: true,
			Name:         "Test App",
			Secret:       "test_secret",
			IsCustomAuth: false,
		},
		{
			ID:           "another_client_id",
			Domain:       "http://another.com",
			IsPrivileged: true,
			Name:         "Another App",
			Secret:       "another_secret",
			IsCustomAuth: false,
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationClient{
		{
			OrganizationID:  1,
			ClientID:        "test_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
		{
			OrganizationID:  2,
			ClientID:        "another_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationWallet{
		{
			OrganizationID: 1,
			WalletType:     "evm",
			WalletAddress:  "******************************************",
		},
		{
			OrganizationID: 1,
			WalletType:     "tron",
			WalletAddress:  "TJ11111111111111111111111111111111",
		},
	}).Error)

	server := gin.Default()
	server.POST("/v1/payment/intent", middleware.PublicHeaderValidation(middleware.HeaderValidation{
		Key:      "X-Client-ID",
		Name:     "client_id",
		Required: true,
	}, middleware.HeaderValidation{
		Key:      "Origin",
		Name:     "origin",
		Required: false,
	}), CreatePaymentIntent)
	server.GET("/v1/payment/intent/:id", middleware.PublicHeaderValidation(middleware.HeaderValidation{
		Key:      "X-Client-ID",
		Name:     "client_id",
		Required: true,
	}), GetPaymentIntent)
	server.POST("/v1/payment/intent/:intent_id/cancel", middleware.PublicHeaderValidation(middleware.HeaderValidation{
		Key:      "X-Client-ID",
		Name:     "client_id",
		Required: true,
	}), CancelPaymentIntent)

	return server, "/v1/payment/intent"
}

func TestCreatePaymentIntent(t *testing.T) {
	server, url := setupPaymentTest(t)

	t.Run("success", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"amount":       "100.0",
			"currency":     "USD",
			"pricing_mode": "fiat",
			"pay_token":    "USDT",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		if w.Code == http.StatusOK {
			var respMap map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &respMap)
			assert.NoError(t, err)
			data, ok := respMap["data"].(map[string]interface{})
			if !ok {
				t.Fatalf("response data is not map[string]interface{}, got: %T, value: %v", respMap["data"], respMap["data"])
			}
			assert.Equal(t, "test_client_id", data["client_id"])
			assert.Equal(t, "100", data["fiat_amount"])
			assert.Equal(t, "USD", data["fiat_currency"])
			assert.Equal(t, "pending", data["status"])
			assert.Equal(t, "fiat", data["pricing_mode"])
			assert.Nil(t, data["callback_url"])
		} else {
			t.Logf("Expected status %d but got %d. Response body: %s", http.StatusOK, w.Code, w.Body.String())
		}
	})

	t.Run("success with callback_url and order data", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"amount":       "100.0",
			"currency":     "USD",
			"pricing_mode": "fiat",
			"pay_token":    "USDT",
			"order_data":   map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)},
			"callback_url": "https://test.com",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		if w.Code == http.StatusOK {
			var respMap map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &respMap)
			assert.NoError(t, err)
			data, ok := respMap["data"].(map[string]interface{})
			if !ok {
				t.Fatalf("response data is not map[string]interface{}, got: %T, value: %v", respMap["data"], respMap["data"])
			}
			assert.Equal(t, "test_client_id", data["client_id"])
			assert.Equal(t, "100", data["fiat_amount"])
			assert.Equal(t, "USD", data["fiat_currency"])
			assert.Equal(t, "pending", data["status"])
			assert.Equal(t, "fiat", data["pricing_mode"])
			assert.Equal(t, "https://test.com", data["callback_url"])
			assert.Equal(t, map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)}, data["order_data"])
		} else {
			t.Logf("Expected status %d but got %d. Response body: %s", http.StatusOK, w.Code, w.Body.String())
		}
	})

	t.Run("success with group_key", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"amount":       "100.0",
			"currency":     "USD",
			"pricing_mode": "fiat",
			"pay_token":    "USDT",
			"group_key":    "checkout_flow",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		if w.Code == http.StatusOK {
			var respMap map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &respMap)
			assert.NoError(t, err)
			data, ok := respMap["data"].(map[string]interface{})
			if !ok {
				t.Fatalf("response data is not map[string]interface{}, got: %T, value: %v", respMap["data"], respMap["data"])
			}
			assert.Equal(t, "test_client_id", data["client_id"])
			assert.Equal(t, "100", data["fiat_amount"])
			assert.Equal(t, "USD", data["fiat_currency"])
			assert.Equal(t, "pending", data["status"])
			assert.Equal(t, "fiat", data["pricing_mode"])
			assert.Equal(t, "checkout_flow", data["group_key"])
		} else {
			t.Logf("Expected status %d but got %d. Response body: %s", http.StatusOK, w.Code, w.Body.String())
		}
	})

	t.Run("success with payout_target_address", func(t *testing.T) {
		requestBody := map[string]any{
			"amount":                "100.0",
			"currency":              "USD",
			"pricing_mode":          "fiat",
			"pay_token":             "USDT",
			"payout_target_address": "******************************************",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		if w.Code == http.StatusOK {
			var respMap map[string]any
			err := json.Unmarshal(w.Body.Bytes(), &respMap)
			assert.NoError(t, err)
			data, ok := respMap["data"].(map[string]any)
			if !ok {
				t.Fatalf("response data is not map[string]interface{}, got: %T, value: %v", respMap["data"], respMap["data"])
			}
			assert.Equal(t, "test_client_id", data["client_id"])
			assert.Equal(t, "100", data["fiat_amount"])
			assert.Equal(t, "USD", data["fiat_currency"])
			assert.Equal(t, "pending", data["status"])
			assert.Equal(t, "fiat", data["pricing_mode"])
			assert.Equal(t, "******************************************", data["payout_target_address"])
		} else {
			t.Logf("Expected status %d but got %d. Response body: %s", http.StatusOK, w.Code, w.Body.String())
		}
	})

	t.Run("missing client ID", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"amount":       "100.0",
			"currency":     "USD",
			"pricing_mode": "fiat",
			"pay_token":    "USDT",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		// req.Header.Set("X-Client-ID", "") // Missing client ID
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("missing origin", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"amount":       "100.0",
			"currency":     "USD",
			"pricing_mode": "fiat",
			"pay_token":    "USDT",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		// req.Header.Set("Origin", "") // Missing origin

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		if w.Code != http.StatusOK {
			t.Logf("Expected status %d but got %d. Response body: %s", http.StatusOK, w.Code, w.Body.String())
		}
		// No response body validation for this case as per original logic if it's just checking status
	})

	t.Run("invalid currency", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"amount":       "100.0",
			"currency":     "EUR",
			"pricing_mode": "fiat",
			"pay_token":    "USDT",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("success with legacy parameters", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"fiat_amount":   "100.0",
			"fiat_currency": "USD",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		if w.Code == http.StatusOK {
			var respMap map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &respMap)
			assert.NoError(t, err)
			data, ok := respMap["data"].(map[string]interface{})
			if !ok {
				t.Fatalf("response data is not map[string]interface{}, got: %T, value: %v", respMap["data"], respMap["data"])
			}
			assert.Equal(t, "test_client_id", data["client_id"])
			assert.Equal(t, "100", data["fiat_amount"])
			assert.Equal(t, "USD", data["fiat_currency"])
			assert.Equal(t, "pending", data["status"])
			assert.Equal(t, "fiat", data["pricing_mode"]) // Legacy always uses fiat mode
		} else {
			t.Logf("Expected status %d but got %d. Response body: %s", http.StatusOK, w.Code, w.Body.String())
		}
	})

	t.Run("success with crypto pricing mode", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"amount":       "3.5",
			"pricing_mode": "crypto",
			"pay_token":    "USDT",
		}
		reqBodyBytes, _ := json.Marshal(requestBody)
		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		if w.Code == http.StatusOK {
			var respMap map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &respMap)
			assert.NoError(t, err)
			data, ok := respMap["data"].(map[string]interface{})
			if !ok {
				t.Fatalf("response data is not map[string]interface{}, got: %T, value: %v", respMap["data"], respMap["data"])
			}
			assert.Equal(t, "test_client_id", data["client_id"])
			assert.Equal(t, "3.5", data["crypto_amount"])
			assert.Equal(t, "crypto", data["pricing_mode"])
			assert.Equal(t, "pending", data["status"])
			assert.Equal(t, "USDT", data["symbol"])
		} else {
			t.Logf("Expected status %d but got %d. Response body: %s", http.StatusOK, w.Code, w.Body.String())
		}
	})
}

func TestGetPaymentIntent(t *testing.T) {
	server, baseURL := setupPaymentTest(t)

	// Create a test payment intent first
	intent, err := payment.CreateIntent(context.Background(), payment.CreateIntentParams{
		Chain:               domain.Arbitrum,
		PayToken:            "USDT",
		ClientID:            "test_client_id",
		Origin:              "http://test.com",
		Amount:              "100.0",
		Currency:            util.Ptr("USD"),
		PricingMode:         payment.PricingModeFiat,
		OrderData:           map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)},
		CallbackURL:         util.Ptr("http://test.com"),
		GroupKey:            util.Ptr("test_group_key"),
		PayoutTargetAddress: util.Ptr("******************************************"),
	})
	assert.Nil(t, err)

	// Create a completed payment intent with payer address and timestamp
	completedIntent, err := payment.CreateIntent(context.Background(), payment.CreateIntentParams{
		Chain:       domain.Arbitrum,
		PayToken:    "USDT",
		ClientID:    "test_client_id",
		Origin:      "http://test.com",
		Amount:      "200.0",
		Currency:    util.Ptr("USD"),
		PricingMode: payment.PricingModeFiat,
	})
	assert.Nil(t, err)

	// Update the completed intent directly in the database
	db := rdb.Get()
	payerAddr := domain.NewEvmAddress("0xabcdef1234567890abcdef1234567890abcdef12")
	txHash := "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
	timestamp := time.Now().UTC()

	dbErr := db.Model(&model.PaymentIntent{}).
		Where("id = ?", completedIntent.ID).
		Updates(map[string]interface{}{
			"status":               string(domain.PaymentIntentStatusSuccess),
			"payer_address":        payerAddr.String(),
			"payment_tx_hash":      txHash,
			"payment_tx_timestamp": timestamp,
		}).Error
	assert.Nil(t, dbErr)

	t.Run("success", func(t *testing.T) {
		url := baseURL + "/" + intent.ID
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "test_client_id")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].(map[string]interface{})
		assert.Equal(t, intent.ID, data["payment_intent_id"])
		assert.Equal(t, "test_client_id", data["client_id"])
		assert.Equal(t, map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)}, data["order_data"])
		assert.Equal(t, "test_group_key", data["group_key"])
		assert.Equal(t, "******************************************", data["payout_target_address"])

		// Verify that payer_address field should not be present for pending intent
		_, payerAddressExists := data["payer_address"]
		assert.False(t, payerAddressExists, "payer_address should not be present for pending intent")

		// Verify that payment_tx_timestamp field should not be present for pending intent
		_, paymentTimestampExists := data["payment_tx_timestamp"]
		assert.False(t, paymentTimestampExists, "payment_tx_timestamp should not be present for pending intent")
	})

	t.Run("success with payer address and timestamp", func(t *testing.T) {
		url := baseURL + "/" + completedIntent.ID
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "test_client_id")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].(map[string]interface{})
		assert.Equal(t, completedIntent.ID, data["payment_intent_id"])
		assert.Equal(t, "test_client_id", data["client_id"])
		assert.Equal(t, "success", data["status"])

		// Verify payer address is present and has the correct value
		assert.Equal(t, payerAddr.String(), data["payer_address"])

		// Verify payment tx timestamp is present
		txTimestamp, exists := data["payment_tx_timestamp"]
		assert.True(t, exists, "payment_tx_timestamp should be present")
		assert.NotNil(t, txTimestamp)

		// Verify payment tx hash
		assert.Equal(t, txHash, data["payment_tx_hash"])
	})

	t.Run("wrong client ID", func(t *testing.T) {
		url := baseURL + "/" + intent.ID
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "another_client_id")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("missing client ID", func(t *testing.T) {
		url := baseURL + "/" + intent.ID
		req, _ := http.NewRequest("GET", url, nil)
		// req.Header.Set("X-Client-ID", "") // Missing client ID

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid intent ID", func(t *testing.T) {
		url := baseURL + "/" + "invalid_id"
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "test_client_id")

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

func TestGetPaymentIntents(t *testing.T) {
	// Initialize mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)

	// Initialize services with mocks
	payment.Init(mockRepo, mockCallbackExecutor, nil)

	// Create test data - this will be used by the mock to filter results
	allIntents := []*domain.PaymentIntent{
		{
			ID:             "intent1",
			ClientID:       "test_client_id",
			OrgID:          1,
			Status:         domain.PaymentIntentStatusPending,
			PaymentChain:   domain.Arbitrum,
			PaymentAddress: domain.NewEvmAddress("0x1111111111111111111111111111111111111111"),
			Symbol:         "USDT",
			Decimals:       6,
			GroupKey:       util.Ptr("checkout"),
		},
		{
			ID:             "intent2",
			ClientID:       "test_client_id",
			OrgID:          1,
			Status:         domain.PaymentIntentStatusSuccess,
			PaymentChain:   domain.Arbitrum,
			PaymentAddress: domain.NewEvmAddress("0x2222222222222222222222222222222222222222"),
			Symbol:         "USDT",
			Decimals:       6,
			GroupKey:       util.Ptr("purchase"),
		},
		{
			ID:             "intent3",
			ClientID:       "another_client_id",
			OrgID:          1,
			Status:         domain.PaymentIntentStatusPending,
			PaymentChain:   domain.Arbitrum,
			PaymentAddress: domain.NewEvmAddress("0x3333333333333333333333333333333333333333"),
			Symbol:         "USDT",
			Decimals:       6,
			GroupKey:       util.Ptr("checkout"),
		},
	}

	// Setup a single flexible mock expectation that handles all test cases
	mockRepo.EXPECT().
		GetPaymentIntents(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, params domain.GetPaymentIntentsParams) ([]*domain.PaymentIntent, int, error) {
			// Test case: repository error
			if params.OrgID == 1 && params.Page == 1 && params.PageSize == 10 &&
				params.ClientID == nil && len(params.Status) == 0 && t.Name() == "TestGetPaymentIntents/repository_error" {
				return nil, 0, errors.New("database error")
			}

			// Filter intents based on params
			filtered := make([]*domain.PaymentIntent, 0)
			for _, intent := range allIntents {
				// Filter by org_id
				if intent.OrgID != params.OrgID {
					continue
				}

				// Filter by client_id if provided
				if params.ClientID != nil && intent.ClientID != *params.ClientID {
					continue
				}

				// Filter by status if provided
				if len(params.Status) > 0 && !slices.Contains(params.Status, intent.Status) {
					continue
				}

				// Filter by group_key if provided
				if params.GroupKey != nil && (intent.GroupKey == nil || !strings.Contains(*intent.GroupKey, *params.GroupKey)) {
					continue
				}

				filtered = append(filtered, intent)
			}

			// Get total count before pagination
			totalCount := len(filtered)

			// Apply pagination
			start := (params.Page - 1) * params.PageSize
			end := start + params.PageSize
			if start >= len(filtered) {
				return []*domain.PaymentIntent{}, totalCount, nil
			}
			if end > len(filtered) {
				end = len(filtered)
			}

			return filtered[start:end], totalCount, nil
		}).
		AnyTimes()

	t.Run("missing org ID", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 0)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid page parameter", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=-1&page_size=10"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid page_size parameter", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=invalid"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid status parameter", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&status=invalid_status"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("all intents for org", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 3, len(data), "Should return all 3 intents for the org")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(3), paging["total_count"], "Total count should be 3")
	})

	t.Run("filter by client_id", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&client_id=test_client_id"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 2, len(data), "Should return intents for test_client_id")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(2), paging["total_count"], "Total count should be 2")
	})

	t.Run("filter by status", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&status=pending"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 2, len(data), "Should return pending intents")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(2), paging["total_count"], "Total count should be 2")
	})

	t.Run("filter by client_id and status", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&client_id=test_client_id&status=success"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 1, len(data), "Should return success intents for test_client_id")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(1), paging["total_count"], "Total count should be 1")
	})

	t.Run("pagination - custom page and size", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=2&page_size=1"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 1, len(data), "Should return one intent for the second page")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(2), paging["page_number"], "Page number should be 2")
		assert.Equal(t, float64(1), paging["page_size"], "Page size should be 1")
		assert.Equal(t, float64(3), paging["total_count"], "Total count should be 3")
	})

	t.Run("empty result set", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&client_id=non_existent_client"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 0, len(data), "Should return empty result set")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(0), paging["total_count"], "Total count should be 0")
	})

	t.Run("filter by group_key", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&group_key=checkout"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 2, len(data), "Should return intents with checkout group_key")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(2), paging["total_count"], "Total count should be 2")
	})

	t.Run("filter by group_key and client_id", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&group_key=checkout&client_id=test_client_id"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 1, len(data), "Should return checkout intents for test_client_id")
		intent := data[0].(map[string]interface{})
		assert.Equal(t, "intent1", intent["payment_intent_id"], "Should be intent1")
		assert.Equal(t, "checkout", intent["group_key"], "Should have checkout group_key")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(1), paging["total_count"], "Total count should be 1")
	})

	t.Run("fuzzy search by group_key", func(t *testing.T) {
		testRouter := gin.Default()
		testRouter.GET("/v1/payment/intents", middleware.Pagination(), func(c *gin.Context) {
			c.Set("org_id", 1)
			GetPaymentIntents(c)
		})
		url := "/v1/payment/intents?page_number=1&page_size=10&group_key=check"
		req, _ := http.NewRequest("GET", url, nil)
		w := httptest.NewRecorder()
		testRouter.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		data := resp["data"].([]interface{})
		assert.Equal(t, 2, len(data), "Should return all intents with group_key containing 'check'")
		paging := resp["paging"].(map[string]interface{})
		assert.Equal(t, float64(1), paging["page_number"], "Page number should be 1")
		assert.Equal(t, float64(10), paging["page_size"], "Page size should be 10")
		assert.Equal(t, float64(2), paging["total_count"], "Total count should be 2")
		for _, item := range data {
			intent := item.(map[string]interface{})
			groupKey := intent["group_key"].(string)
			assert.Contains(t, groupKey, "check", "Group key should contain 'check'")
		}
	})
}

func TestCreatePaymentIntentWithCryptoPricing(t *testing.T) {
	router, basePath := setupPaymentTest(t)

	// Test case 1: USDT crypto pricing
	t.Run("USDT crypto pricing", func(t *testing.T) {
		reqBody := `{
			"amount": "3.5",
			"pricing_mode": "crypto",
			"pay_token": "USDT",
			"order_data": {"order_id": "12345"},
			"callback_url": "http://example.com/callback",
			"group_key": "test-group"
		}`

		req, _ := http.NewRequest("POST", basePath, strings.NewReader(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify response status code
		assert.Equal(t, http.StatusOK, w.Code)

		// Verify the response body
		var resp struct {
			Code    int            `json:"code"`
			Message string         `json:"message"`
			Data    payment.Intent `json:"data"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		assert.Equal(t, 0, resp.Code)
		assert.Equal(t, "USDT", resp.Data.Symbol)
		assert.Equal(t, "pending", resp.Data.Status)
		assert.Equal(t, "3.5", resp.Data.CryptoAmount)
		assert.Equal(t, "crypto", resp.Data.PricingMode)
		assert.Equal(t, "test-group", *resp.Data.GroupKey)
		assert.Equal(t, "http://example.com/callback", *resp.Data.CallbackURL)
		assert.Equal(t, map[string]interface{}{"order_id": "12345"}, resp.Data.OrderData)
	})

	// Test case 2: USDC crypto pricing
	t.Run("USDC crypto pricing", func(t *testing.T) {
		reqBody := `{
			"amount": "10.0",
			"pricing_mode": "crypto",
			"pay_token": "USDC",
			"order_data": {"order_id": "crypto-usdc-order"},
			"callback_url": "http://example.com/crypto-callback",
			"group_key": "crypto-test"
		}`

		req, _ := http.NewRequest("POST", basePath, strings.NewReader(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Client-ID", "test_client_id")
		req.Header.Set("Origin", "http://test.com")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify response status code
		assert.Equal(t, http.StatusOK, w.Code, "Expected status 200 but got %d with body: %s", w.Code, w.Body.String())

		// If status is not OK, print more debug info and skip further checks
		if w.Code != http.StatusOK {
			t.Logf("Response body: %s", w.Body.String())
			return
		}

		// Use a new response object for the second test
		var resp struct {
			Code    int            `json:"code"`
			Message string         `json:"message"`
			Data    payment.Intent `json:"data"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		assert.Equal(t, 0, resp.Code, "Expected code 0 but got %d with message: %s", resp.Code, resp.Message)

		// Only proceed with further assertions if the response code is as expected
		if resp.Code == 0 {
			assert.Equal(t, "USDC", resp.Data.Symbol)
			assert.Equal(t, "pending", resp.Data.Status)
			assert.Equal(t, "10", resp.Data.CryptoAmount)
			assert.Equal(t, "crypto", resp.Data.PricingMode)

			// Check for nil pointers before dereferencing
			if resp.Data.GroupKey != nil {
				assert.Equal(t, "crypto-test", *resp.Data.GroupKey)
			} else {
				assert.Fail(t, "GroupKey is nil")
			}

			if resp.Data.CallbackURL != nil {
				assert.Equal(t, "http://example.com/crypto-callback", *resp.Data.CallbackURL)
			} else {
				assert.Fail(t, "CallbackURL is nil")
			}

			assert.Equal(t, map[string]interface{}{"order_id": "crypto-usdc-order"}, resp.Data.OrderData)
		}
	})
}

func TestCreatePaymentIntentLegacyParams(t *testing.T) {
	server, url := setupPaymentTest(t)

	requestBody := map[string]interface{}{
		"fiat_amount":   "100",
		"fiat_currency": "USD",
		"order_data":    map[string]interface{}{"order_id": "legacy-order"},
		"callback_url":  "http://example.com/legacy",
		"group_key":     "legacy-group",
	}

	reqBodyBytes, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Client-ID", "test_client_id")
	req.Header.Set("Origin", "http://test.com")

	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var respMap map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &respMap)
	assert.NoError(t, err)

	data := respMap["data"].(map[string]interface{})
	assert.Equal(t, "USDT", data["symbol"])
	assert.Equal(t, "pending", data["status"])
	assert.Equal(t, "100", data["fiat_amount"])
	assert.Equal(t, "USD", data["fiat_currency"])
	assert.Equal(t, "legacy-group", data["group_key"])
	assert.Equal(t, "http://example.com/legacy", data["callback_url"])
	assert.Equal(t, map[string]interface{}{"order_id": "legacy-order"}, data["order_data"])
}

func TestCreatePaymentIntentMixedParameters(t *testing.T) {
	server, url := setupPaymentTest(t)

	// Test when both old and new parameters are provided, new parameters should take precedence
	requestBody := map[string]interface{}{
		"amount":        "200",
		"currency":      "TWD",
		"pricing_mode":  "fiat",
		"pay_token":     "USDT",
		"fiat_amount":   "100",
		"fiat_currency": "USD",
		"order_data":    map[string]interface{}{"order_id": "mixed-order"},
		"callback_url":  "http://example.com/mixed",
		"group_key":     "mixed-group",
	}

	reqBodyBytes, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(reqBodyBytes))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Client-ID", "test_client_id")
	req.Header.Set("Origin", "http://test.com")

	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	// Log response for debugging
	t.Logf("Response status: %d", w.Code)
	t.Logf("Response body: %s", w.Body.String())

	assert.Equal(t, http.StatusOK, w.Code)

	// Only proceed with testing the response if we got a successful status
	if w.Code == http.StatusOK {
		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		data, ok := respMap["data"].(map[string]interface{})
		assert.True(t, ok, "Response data should be a map")

		// Should use the new parameters instead of legacy
		assert.Equal(t, "USDT", data["symbol"])
		assert.Equal(t, "pending", data["status"])
		assert.Equal(t, "200", data["fiat_amount"])   // From amount parameter
		assert.Equal(t, "TWD", data["fiat_currency"]) // From currency parameter
		assert.Equal(t, "mixed-group", data["group_key"])
		assert.Equal(t, "http://example.com/mixed", data["callback_url"])

		// For nested map, check if it exists and if it matches
		orderData, ok := data["order_data"].(map[string]interface{})
		assert.True(t, ok, "order_data should be a map")
		assert.Equal(t, "mixed-order", orderData["order_id"])
	} else {
		t.Logf("Skipping response validation due to non-200 status code")
	}
}

func TestCreatePaymentIntentNewParams(t *testing.T) {
	router, basePath := setupPaymentTest(t)

	reqBody := `{
		"amount": "200",
		"currency": "TWD",
		"pricing_mode": "fiat",
		"pay_token": "USDT",
		"order_data": {"order_id": "new-order"},
		"callback_url": "http://example.com/new",
		"group_key": "new-group"
	}`

	req, _ := http.NewRequest("POST", basePath, strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Client-ID", "test_client_id")
	req.Header.Set("Origin", "http://test.com")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Log response for debugging
	t.Logf("Response status: %d", w.Code)
	t.Logf("Response body: %s", w.Body.String())

	// Verify response status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Only proceed with testing the response if we got a successful status
	if w.Code == http.StatusOK {
		// Verify the response body
		var resp struct {
			Code    int            `json:"code"`
			Message string         `json:"message"`
			Data    payment.Intent `json:"data"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		assert.Equal(t, 0, resp.Code)

		// Confirm that the service layer received and processed the request correctly
		assert.Equal(t, "USDT", resp.Data.Symbol)
		assert.Equal(t, "pending", resp.Data.Status)

		// Check for nil before dereferencing
		assert.NotNil(t, resp.Data.FiatAmount, "FiatAmount should not be nil")
		if resp.Data.FiatAmount != nil {
			assert.Equal(t, "200", *resp.Data.FiatAmount)
		}

		assert.NotNil(t, resp.Data.FiatCurrency, "FiatCurrency should not be nil")
		if resp.Data.FiatCurrency != nil {
			assert.Equal(t, "TWD", *resp.Data.FiatCurrency)
		}

		// Check for nil pointer before dereferencing
		assert.NotNil(t, resp.Data.GroupKey, "GroupKey should not be nil")
		if resp.Data.GroupKey != nil {
			assert.Equal(t, "new-group", *resp.Data.GroupKey)
		}
	} else {
		t.Logf("Skipping response validation due to non-200 status code")
	}
}

func TestCreatePaymentIntentMissingParams(t *testing.T) {
	router, basePath := setupPaymentTest(t)

	// Test case: Missing both amount and fiat_amount
	reqBody := `{
		"order_data": {"order_id": "missing-params"},
		"callback_url": "http://example.com/missing",
		"group_key": "missing-group"
	}`

	req, _ := http.NewRequest("POST", basePath, strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Client-ID", "test_client_id")
	req.Header.Set("Origin", "http://test.com")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify response status code - should be bad request
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Test case: Missing currency when amount is provided
	reqBody = `{
		"amount": "100",
		"order_data": {"order_id": "missing-currency"},
		"callback_url": "http://example.com/missing",
		"group_key": "missing-group"
	}`

	req, _ = http.NewRequest("POST", basePath, strings.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Client-ID", "test_client_id")
	req.Header.Set("Origin", "http://test.com")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Verify response status code - should be bad request
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestCancelPaymentIntent(t *testing.T) {
	server, _ := setupPaymentTest(t)

	t.Run("missing client ID", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/v1/payment/intent/test-intent-123/cancel", nil)
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "missing X-Client-ID header")
	})

	t.Run("missing intent ID", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/v1/payment/intent//cancel", nil)
		req.Header.Set("X-Client-ID", "test-client-123")
		server.ServeHTTP(w, req)

		// This should result in a 400 since the middleware catches the missing header
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("valid request format", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/v1/payment/intent/test-intent-123/cancel", nil)
		req.Header.Set("X-Client-ID", "test-client-123")
		server.ServeHTTP(w, req)

		// This will likely return an error since the intent doesn't exist,
		// but it should not be a validation error (400) - it should be 404 or 500
		assert.NotEqual(t, http.StatusBadRequest, w.Code)
	})
}
