package router

import (
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis_rate/v9"
	"github.com/kryptogo/kg-wallet-backend/api"
	aaveapi "github.com/kryptogo/kg-wallet-backend/api/aave"
	assetprice "github.com/kryptogo/kg-wallet-backend/api/asset-price"
	authapi "github.com/kryptogo/kg-wallet-backend/api/auth"
	"github.com/kryptogo/kg-wallet-backend/api/balance"
	"github.com/kryptogo/kg-wallet-backend/api/binance"
	"github.com/kryptogo/kg-wallet-backend/api/bridge"
	"github.com/kryptogo/kg-wallet-backend/api/chatroom"
	"github.com/kryptogo/kg-wallet-backend/api/compliance"
	"github.com/kryptogo/kg-wallet-backend/api/contact"
	defiswap "github.com/kryptogo/kg-wallet-backend/api/defi-swap"
	ephemeralnoteapi "github.com/kryptogo/kg-wallet-backend/api/ephemeral-note"
	gasfaucetapi "github.com/kryptogo/kg-wallet-backend/api/gas-faucet"
	gasswapapi "github.com/kryptogo/kg-wallet-backend/api/gas-swap"
	gaslesssendapi "github.com/kryptogo/kg-wallet-backend/api/gasless-send"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/nft"
	oauthapi "github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/payment"
	"github.com/kryptogo/kg-wallet-backend/api/payment_item"
	"github.com/kryptogo/kg-wallet-backend/api/quest"
	"github.com/kryptogo/kg-wallet-backend/api/referral"
	sendwithfee "github.com/kryptogo/kg-wallet-backend/api/send-with-fee"
	sendwithrent "github.com/kryptogo/kg-wallet-backend/api/send-with-rent"
	tokenanalysis "github.com/kryptogo/kg-wallet-backend/api/token-analysis"
	tokensignal "github.com/kryptogo/kg-wallet-backend/api/token-signal"
	txwatchapi "github.com/kryptogo/kg-wallet-backend/api/tx-watch"
	universalswap "github.com/kryptogo/kg-wallet-backend/api/universal-swap"
	"github.com/kryptogo/kg-wallet-backend/api/user"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
)

// firebase hosting rewrite apis
func registerV1API(v1 *gin.RouterGroup) {
	v1.GET("/ok", api.GetOk)
	v1.GET("/auth/ok",
		auth.AuthorizeByTokenOrOAuth([]string{}),
		api.GetOk)

	// nft
	v1.Any("/nft/tag/:type/:chain/:address/:token_id",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		nft.AddOrDeleteNftTag)
	v1.GET("/nfts",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.Nfts)
	v1.POST("/nfts/update",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.UpdateNfts)
	v1.GET("/nfts_by_slug/:slug",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.NftsBySlug)
	v1.GET("/nfts/value",
		middleware.DeprecatedInfo, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.NftsValue)
	v1.GET("/nft_collections",
		middleware.DeprecatedInfo, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.NftCollections)
	v1.GET("/nfts_received",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.NftsReceived)
	v1.POST("/nfts/mint", auth.ContractAddressJWTToken, nft.NftMint91APP)
	v1.GET("/nfts_by_collections",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.NftsByCollections)
	v1.POST("/nft/:chain/:address/:token_id/refresh",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		nft.RefreshNft)
	v1.GET("/nft/:chain_id/:contract_address/:token_id/details",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		nft.NftDetail)

	// coupon - nonauth
	v1.POST("/getCouponsByNft", middleware.DeprecatedWarn, api.CouponsByNft)
	v1.POST("/getCouponById", middleware.DeprecatedWarn, api.CouponByID)
	v1.POST("/redeemCouponByNft", middleware.DeprecatedWarn, api.RedeemCouponByNft)

	// prize(coupon) - user auth
	v1.GET("/prizes",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.PrizesByUser)
	v1.GET("/prize/:prize_id/token_id/:token_id",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.UserPrize)
	v1.Any("/prize/:prize_id/token_id/:token_id/tag/:type",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		api.PrizeTag)
	v1.POST("/prize/redeem", api.PrizeRedeem) // no auth

	// prize(coupon) - for web (no auth)
	v1.GET("/project/:project_id/prizes", api.PrizesByProject)
	v1.GET("/nft/:chain_id/:contract_address/:token_id/prizes", api.PrizesByNft) // replace getCouponsByNft

	// user
	v1.DELETE("/user", auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserDelete}),
		user.DeleteUser)
	v1.PUT("/user/avatar",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoWrite}),
		user.SetAvatar)
	v1.POST("/user/avatar",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoWrite}),
		middleware.RateLimitByUserUID("upload_avatar", redis_rate.PerMinute(5)),
		user.UploadAvatar)
	v1.POST("/user/retrieveAndDeleteMnemonic",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeVaultWrite}),
		user.RetrieveAndDeleteMnemonic)
	v1.POST("/user/phone_numbers",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}),
		user.GetUsersByPhoneNumbers)
	v1.GET("/user/profile_home",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}),
		user.ProfileHome)
	v1.GET("/user/info",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}),
		user.GetUserInfo)
	v1.PUT("/user/info",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoWrite}),
		user.UpdateUserInfo)
	v1.GET("/user/kyc_state",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserKycStateRead}),
		user.GetUserKycState)
	v1.PUT("/user/password",
		auth.AuthorizeByKgTokenOrOAuth([]string{oauth.ScopeUserPasswordWrite}),
		user.UpdatePassword)
	v1.PUT("/user/phone_number",
		auth.AuthorizeByKgToken(),
		user.UpdatePhoneNumber)
	v1.PUT("/user/email",
		auth.AuthorizeByKgToken(),
		user.UpdateEmail)
	v1.POST("/user/data_exist", auth.AuthorizeByKgToken(), user.DataExist)
	v1.GET("/user/password_salt_frontend", user.GetPasswordSaltFrontend)
	v1.GET("/user/organizations",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		user.Organizations,
	)

	// wallets
	v1.GET("/user/wallets/all",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeWalletDefaultWalletsRead}),
		user.GetAllUserWallets)
	v1.PUT("/user/wallets/all",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeWalletAllWalletsWrite}),
		user.UpsertUserWallets)
	v1.DELETE("/user/wallets/all",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeWalletAllWalletsWrite}),
		user.DeleteUserWallets)
	v1.POST("/user/wallets/all",
		auth.AuthorizeByKgToken(),
		user.CreateUserWallets)

	v1.GET("/user/wallets",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		user.GetUserWallets)
	v1.GET("/user/vault_data",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeVaultRead}),
		user.GetUserVaultData)
	v1.PUT("/user/vault_data",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeVaultWrite}),
		user.UpsertUserVaultData)
	v1.DELETE("/user/vault_data",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeVaultWrite}),
		user.DeleteUserVaultData)

	v1.GET("/user/share_key",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeVaultRead}),
		user.GetUserShareKey)
	v1.PUT("/user/share_key",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeVaultWrite}),
		user.UpsertUserShareKey)
	v1.DELETE("/user/share_key",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeVaultWrite}),
		user.DeleteUserShareKey)

	v1.GET("/user/order_check",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		user.GetUserOrderCheck)
	v1.GET("/user/crypto/agree_tos",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		user.GetCryptoAgreeTos)
	v1.POST("/user/crypto/agree_tos",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		user.CreateCryptoAgreeTos)
	v1.GET("/user/receive_wallet_ens",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeWalletDefaultWalletsRead}),
		user.GetReceiveWalletEns)

	v1.GET("/contacts",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}),
		contact.ListContacts)
	v1.PUT("/contacts",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoWrite}),
		contact.UpsertContacts)
	v1.DELETE("/contacts/:contact_id",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoWrite}),
		contact.DeleteContact)

	// notification
	v1.GET("/notifications/unread/count",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeNotificationRead}),
		api.NotificationUnreadCount)
	v1.GET("/notifications",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeNotificationRead}),
		api.Notifications)
	v1.GET("/notification/:nid",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeNotificationRead}),
		api.NotificationDetail)
	v1.POST("/notifications/readall",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeNotificationWrite}),
		api.NotificationReadAll)

	// asset
	v1.GET("/assets",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.Assets)
	v1.POST("/assets/update",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.UpdateAssets)
	v1.GET("/assets/single",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.SingleAsset)
	v1.GET("/asset_prices",
		middleware.AuthByTokenOrStudioTokenV2,
		assetprice.AssetPrices)
	v1.GET("/tokens/pinned",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.GetPinnedTokens)

	// balance
	v1.GET("/balances",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		balance.Balances)
	v1.GET("/balances/summary",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		balance.BalancesSummary)

	// tx
	v1.GET("/txlists",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeTransactionRead}),
		api.TxLists)
	v1.POST("/txlists/update",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeTransactionRead}),
		api.UpdateTxList)
	v1.GET("/tx/:chain_id/:address/:hash",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeTransactionRead}),
		api.TxDetail)

	// kyc
	v1.GET("/kyc",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserKycStateRead}),
		compliance.GetKycResult)
	v1.POST("/kyc",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserKycStateWrite}),
		compliance.InitIdv)
	v1.GET("/kyc/accepted_id_types",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserKycStateRead}),
		compliance.GetAcceptedIDTypes)
	v1.POST("/kyc/idv_callback", compliance.IdvCallback)
	v1.POST("/kyc/search_task_callback", compliance.SearchTaskCallback)

	// order
	v1.POST("/orders",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeOrderWrite}),
		api.CreateOrder)
	v1.GET("/orders",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeOrderRead}),
		api.ListOrders)
	v1.GET("/orders/:order_id",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeOrderRead}),
		api.GetOrderDetail)
	v1.POST("/orders/stripe_webhook", api.StripeOrderWebhook)

	// auth
	v1.POST("/auth/user_exist", authapi.UserExist)
	v1.POST("/sms", authapi.SmsCode)
	v1.POST("/register", authapi.Register)
	v1.POST("/login", authapi.Login)
	v1.POST("/login/google",
		auth.AuthorizeByOptionalOAuth([]string{oauth.ScopeUserInfoWrite}),
		oauthapi.GoogleLogin)
	v1.POST("/email", authapi.EmailCode)
	v1.POST("/login/custom_auth", authapi.CustomAuthLogin)

	// websocket
	v1.GET("/ws", api.HandleWSRequest)

	// market
	v1.GET("/market/tokens", api.GetTokens)
	v1.GET("/market/prices",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.GetPrice)
	v1.GET("/market/config",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.GetBuyCryptoConfig)
	v1.GET("/market/order_limits",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.GetOrderLimits)
	v1.GET("/market/swap_config",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.GetSwapConfig)
	v1.POST("/market/swap_tx",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}),
		api.NotifyDefiSwap)

	// oauth
	v1.GET("/oauth/authorize", oauthapi.AuthorizeHandler)
	v1.POST("/oauth/token", oauthapi.TokenHandler)
	v1.POST("/oauth/refresh", oauthapi.RefreshToken)
	v1.GET("/oauth/google_callback", oauthapi.GoogleLoginCallback)
	v1.GET("/oauth/callback", oauthapi.CallbackHandler)
	v1.GET("/oauth/configs", oauthapi.GetOAuthClientConfig)
	// exchange OAuth token using KG-WALLET-TOKEN
	v1.POST("/oauth/exchange_token",
		auth.AuthorizeByToken(),
		oauthapi.ExchangeToken)
	// exchange KG-TOKEN using OAuth token, assume all users have user.info:read
	v1.POST("/oauth/exchange_kg_token",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}),
		authapi.ExchangeKgToken)

	// chatroom
	v1.POST("/chatrooms/session_token",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeChatroomRead}),
		chatroom.SessionToken)
	v1.PATCH("/chatrooms/user",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoWrite}),
		chatroom.UpdateUser)
	v1.GET("/chatrooms",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeChatroomRead}),
		chatroom.List)
	v1.POST("/chatrooms/signed_urls",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeChatroomWrite}),
		chatroom.SignedUrls)

	// gas swap
	v1GasSwap := v1.Group("/gas_swap")
	v1GasSwap.GET("/available_chains",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGasSwapRead}),
		gasswapapi.GetAvailableChains)
	v1GasSwap.GET("/available_assets",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGasSwapRead}),
		gasswapapi.GetAvailableAssets)
	v1GasSwap.GET("/quote",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGasSwapRead}),
		gasswapapi.GetQuote)
	v1GasSwap.POST("/init",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGasSwapWrite}),
		gasswapapi.Init)
	v1GasSwap.GET("/:id",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGasSwapRead}),
		gasswapapi.GetByID)

	// tx watch
	v1TxWatch := v1.Group("/tx_watch")
	v1TxWatch.POST("/notify",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeTransactionRead}), middleware.RateLimitByUserUID("tx_watch_notify", redis_rate.PerMinute(10)), txwatchapi.Create)

	// gas faucet
	v1.POST("/gas_faucet",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGasFaucetWrite}),
		gasfaucetapi.Create)

	// aave
	v1.GET("/aave/withdraw_info",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}),
		aaveapi.GetWithdrawInfo)
	v1.POST("/aave/fee_transaction",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoWrite}),
		aaveapi.CreateFeeTransaction)

	{
		v1GaslessSend := v1.Group("/gasless_send")
		// gasless send
		v1GaslessSend.GET("/quote",
			auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGaslessSendRead}),
			gaslesssendapi.GetQuote)
		v1GaslessSend.POST("/",
			auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGaslessSendWrite}),
			gaslesssendapi.Init)
		v1GaslessSend.GET("/:id",
			auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGaslessSendRead}),
			gaslesssendapi.GetByID)

		v2GaslessSend := v1.Group("/gasless_send_v2")
		v2GaslessSend.GET("profit_margin_rate",
			auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGaslessSendRead}),
			gaslesssendapi.GetProfitMarginRate)
		v2GaslessSend.POST("/init",
			auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGaslessSendWrite}),
			gaslesssendapi.InitV2)
		v2GaslessSend.GET("/:id",
			auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeGaslessSendRead}),
			gaslesssendapi.GetByIDV2)
	}

	// ephemeral note
	v1.GET("/ephemeral_note/config",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteRead}),
		ephemeralnoteapi.GetConfig)
	v1.GET("/ephemeral_owner",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteRead}),
		ephemeralnoteapi.GetEphemeralOwner)
	v1.POST("/ephemeral_owner/release",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteWrite}),
		ephemeralnoteapi.ReleaseEphemeralOwner)
	v1.POST("/ephemeral_note",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteWrite}),
		ephemeralnoteapi.Create)
	v1.POST("/ephemeral_note/:id/claim",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteWrite}),
		ephemeralnoteapi.Claim)
	v1.GET("/ephemeral_notes/active",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteRead}),
		ephemeralnoteapi.ListActiveNotes)
	v1.GET("/ephemeral_note/:id",
		ephemeralnoteapi.GetEphemeralNote)
	v1.GET("/ephemeral_notes",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteRead}),
		ephemeralnoteapi.ListAllNotes)

	v1.GET("/send_with_fee/profit_margin_rate",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeSendWithFeeRead}),
		sendwithfee.GetProfitMarginRate)
	v1.POST("/send_with_fee/tx",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeSendWithFeeWrite}),
		sendwithfee.NotifyTransaction)

	// proxy
	v1.POST("/proxy_3rd_party",
		middleware.AuthByTokenOrStudioTokenV2,
		api.Proxy3rdParty)

	// send link campaign
	v1.GET("/send_link_campaign/status",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeEphemeralNoteRead}),
		ephemeralnoteapi.GetSendLinkCampaignStatus)

	// send with rent
	v1SendWithRent := v1.Group("/send_with_rent")
	v1SendWithRent.POST("/init",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeSendWithRentWrite}),
		sendwithrent.Init)
	v1SendWithRent.GET("/:id",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeSendWithRentRead}),
		sendwithrent.GetByID)

	// binance pay
	v1.POST("/binance_pay/withdraw_url",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		binance.CreateWithdrawURL)
	v1.POST("/binance_pay/deposit_url",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		binance.CreateDepositURL)
	v1.POST("/binance_pay/deposit_address",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		binance.QueryDepositAddress)
	v1.POST("/binance_pay/deposit_report",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetWrite}),
		binance.ReportDeposit)

	// payment intent - public methods
	v1.POST("/payment/intent", middleware.PublicHeaderValidation(middleware.HeaderValidation{
		Key:      "X-Client-ID",
		Name:     "client_id",
		Required: true,
	}, middleware.HeaderValidation{
		Key:      "Origin",
		Name:     "origin",
		Required: false,
	}), payment.CreatePaymentIntent)
	v1.GET("/payment/intent/:id", middleware.PublicHeaderValidation(middleware.HeaderValidation{
		Key:      "X-Client-ID",
		Name:     "client_id",
		Required: true,
	}), payment.GetPaymentIntent)
	v1.POST("/payment/intent/:intent_id/cancel", middleware.PublicHeaderValidation(middleware.HeaderValidation{
		Key:      "X-Client-ID",
		Name:     "client_id",
		Required: true,
	}), payment.CancelPaymentIntent)
	v1.GET("/payment/quote", middleware.PublicHeaderValidation(middleware.HeaderValidation{
		Key:      "X-Client-ID",
		Name:     "client_id",
		Required: false,
	}), payment.GetQuote)

	// payment item - public methods
	v1.GET("/payment/items/:id", payment_item.GetPaymentItemHandler(false))

	// bridge
	v1.GET("/bridge/fee_info", bridge.GetFeeInfo)
	v1.POST("/bridge/record", auth.AuthorizeByOptionalOAuth([]string{oauth.ScopeBridgeRecordWrite}), bridge.CreateBridgeRecord)
	v1.PUT("/bridge/record/:from_tx_hash", bridge.UpdateBridgeRecord)

	// Defi swap
	v1.GET("/defi_swap/fee_info", defiswap.GetFeeInfo)
	v1.POST("/defi_swap/record", auth.AuthorizeByOptionalOAuth([]string{oauth.ScopeDefiSwapRecordWrite}), defiswap.CreateDefiSwapRecord)

	// universal swap
	v1.GET("/universal_swap/deposit_addresses",
		auth.AuthorizeByOptionalOAuth([]string{oauth.ScopeUniversalSwapRead}),
		universalswap.GetDepositAddresses)
	v1.POST("/universal_swap/init",
		auth.AuthorizeByOptionalOAuth([]string{oauth.ScopeUniversalSwapWrite}),
		universalswap.Init)
	v1.POST("/universal_swap/collect",
		auth.AuthorizeByOptionalOAuth([]string{oauth.ScopeUniversalSwapWrite}),
		universalswap.Collect)
	v1.GET("/universal_swap/:id",
		auth.AuthorizeByOptionalOAuth([]string{oauth.ScopeUniversalSwapRead}),
		universalswap.GetByID)

	// referral
	v1Referral := v1.Group("/referral")
	v1Referral.POST("/rewards",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeReferralWrite}),
		referral.RecordReward)
	v1Referral.GET("/rewards",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeReferralRead}),
		referral.GetBalance)
	v1Referral.POST("/withdraw",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeReferralWrite}),
		referral.Withdraw)

	// Galxe quests endpoints
	v1Quest := v1.Group("/quest")
	v1Quest.POST("/address_exists", auth.AuthorizeByOptionalToken(), quest.AddressExists)

	// Token Signal APIs
	tokenSignal := v1.Group("/token_signal")
	tokenSignal.GET("/buy", tokensignal.ListBuySignals)
	tokenSignal.GET("/sell", tokensignal.ListSellSignals)
	tokenSignal.GET("/stats", tokensignal.GetSignalStats)

	// Token Analysis APIs
	tokenAnalysis := v1.Group("/token_analysis")
	tokenAnalysis.POST("/analyze", tokenanalysis.AnalyzeToken)
	tokenAnalysis.GET("/credits/:wallet_address", tokenanalysis.GetCredits)
	tokenAnalysis.POST("/purchase_credits", tokenanalysis.PurchaseCredits)
	tokenAnalysis.POST("/free_credit/:wallet_address", tokenanalysis.GetFreeCredit)
}
