package router

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api"
	authapi "github.com/kryptogo/kg-wallet-backend/api/auth"
	bridgeapi "github.com/kryptogo/kg-wallet-backend/api/bridge"
	freetransferapi "github.com/kryptogo/kg-wallet-backend/api/free-transfer"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/payment"
	"github.com/kryptogo/kg-wallet-backend/api/payment_item"
	studioapi "github.com/kryptogo/kg-wallet-backend/api/studio"
	assetproapi "github.com/kryptogo/kg-wallet-backend/api/studio/assetpro"
	marketapi "github.com/kryptogo/kg-wallet-backend/api/studio/assetpro/market"
	complianceapi "github.com/kryptogo/kg-wallet-backend/api/studio/compliance"
	linebotapi "github.com/kryptogo/kg-wallet-backend/api/studio/linebot"
	nftprojects "github.com/kryptogo/kg-wallet-backend/api/studio/nft-projects"
	"github.com/kryptogo/kg-wallet-backend/api/studio/organization"
	studiouser "github.com/kryptogo/kg-wallet-backend/api/studio/user"
	user360api "github.com/kryptogo/kg-wallet-backend/api/studio/user360"
	studioWalletapi "github.com/kryptogo/kg-wallet-backend/api/studio/wallet"
	"github.com/kryptogo/kg-wallet-backend/api/user"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
)

func registerStudioAPI(v1 *gin.RouterGroup) {
	studio := v1.Group("/studio")
	// common
	studio.GET("/ok", api.GetOk)
	// Self-service organization creation (user auth only, no org required)
	studio.POST("/organizations/create", auth.AuthorizeByKgTokenOrKgStudioToken(), organization.CreateOrg)

	studioAuthV2 := studio.Group("", middleware.AuthStudioTokenV2)
	orgAuth := studioAuthV2.Group("/organization/:orgID", middleware.OrganizationUserValidation())

	// Free transfer
	orgAuth.GET("/free_transfer/status", freetransferapi.GetFreeSendStatus)

	// API Keys
	orgAuth.POST("/api_keys", studiouser.CreateAPIKey)
	orgAuth.GET("/api_keys", middleware.Pagination(), studiouser.ListAPIKeys)
	orgAuth.DELETE("/api_keys/:id", studiouser.DeleteAPIKey)

	// OAuth client creation (requires existing organization)
	orgAuth.POST("/oauth_clients", organization.CreateOAuthClient)
	orgAuth.GET("/oauth_clients", middleware.Pagination(), organization.ListOAuthClients)
	orgAuth.GET("/oauth_clients/:client_id", organization.GetOAuthClient)
	orgAuth.PUT("/oauth_clients/:client_id", organization.UpdateOAuthClient)

	// payment intent - studio methods
	orgAuth.GET("/payment/intents", middleware.Pagination(), payment.GetPaymentIntents)
	orgAuth.POST("/payment/intents/:id/refund", payment.RefundPaymentIntent)
	orgAuth.GET("/payment/dashboard", payment.GetDashboard)

	// bridge service
	orgAuth.POST("/bridge/organization", bridgeapi.CreateBridgeOrganization)
	orgAuth.GET("/bridge/organization", bridgeapi.GetBridgeOrganization)
	orgAuth.POST("/bridge/external_account", bridgeapi.CreateBridgeExternalAccount)
	orgAuth.GET("/bridge/external_accounts", bridgeapi.GetBridgeExternalAccounts)
	orgAuth.GET("/bridge/external_account/:external_account_id", bridgeapi.GetBridgeExternalAccount)
	orgAuth.POST("/bridge/transfer", bridgeapi.CreateBridgeTransfer)
	orgAuth.GET("/bridge/transfers", bridgeapi.GetBridgeTransfers)

	// callback logs and testing - studio methods
	orgAuth.GET("/payment/callbacks", middleware.Pagination(), payment.GetCallbackLogs)
	orgAuth.GET("/payment/callbacks/:id", payment.GetCallbackLogByID)
	orgAuth.POST("/payment/callbacks/test", payment.TestCallback)

	// payment items - studio methods
	orgAuth.POST("/payment/items", payment_item.CreatePaymentItem)
	orgAuth.GET("/payment/items", middleware.Pagination(), payment_item.GetPaymentItems)
	orgAuth.GET("/payment/items/:id", payment_item.GetPaymentItemHandler(true))
	orgAuth.PUT("/payment/items/:id", payment_item.UpdatePaymentItem)
	orgAuth.DELETE("/payment/items/:id", payment_item.DeletePaymentItem)

	studio.POST("/organization/:orgID/signed_urls",
		middleware.AuthByTokenOrStudioTokenV2,
		middleware.OrganizationUserOrCustomerValidation(),
		studioapi.CreateSignedUrls)
	// compliance
	orgAuth.POST("/compliance_token", complianceapi.ComplianceToken)

	// member
	// FIXME: should be deprecated
	orgAuth.GET("/account", studiouser.Account)

	// nft
	orgAuth.PUT("/nft/projects", nftprojects.UpsertNftProject)
	orgAuth.GET("/nft/projects", nftprojects.GetAllNftProjects)
	orgAuth.GET("/nft/projects/:project_id", nftprojects.GetNftProject)

	// boost
	studioAuthV2.GET("/boost/projects", api.Projects)
	studioAuthV2.POST("/boost/project", api.CreateProject)
	studioAuthV2.GET("/boost/project/:project_id", api.ProjectByID)
	studioAuthV2.POST("/boost/project/:project_id/publish", api.TBD)
	studioAuthV2.GET("/boost/project/:project_id/prizes", api.Prizes)
	studioAuthV2.POST("/boost/project/:project_id/prize", api.AddPrize)
	studioAuthV2.POST("/boost/prize/:prize_id", api.UpdatePrize)
	studioAuthV2.GET("/boost/prize/:prize_id", api.PrizeByID)
	studioAuthV2.DELETE("/boost/prize/:prize_id", api.DeletePrize)
	studioAuthV2.POST("/boost/prize/:prize_id/publish", api.PublishPrize)
	studioAuthV2.GET("/boost/prize/:prize_id/redeem_summary", api.RedeemSummary)
	studioAuthV2.GET("/boost/prize/:prize_id/redeem_detail", api.RedeemDetail)
}

func registerStudioWalletAPI(v1 *gin.RouterGroup) {
	orgAuth := v1.Group("/studio/organization/:orgID", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation())
	wallet := orgAuth.Group("/wallet")

	wallet.GET("/ok", api.GetOk)
	wallet.PUT("/projects", studioWalletapi.UpsertWalletProject)
	wallet.GET("/projects", studioWalletapi.GetWalletProjects)

	// Add new endpoints for wallet addresses management
	wallet.GET("/addresses", studioWalletapi.GetImportedAddresses)
	wallet.POST("/addresses", studioWalletapi.ImportAddress)
	wallet.DELETE("/addresses/:addressID", studioWalletapi.DeleteImportedAddress)
	wallet.PUT("/addresses/:addressID/default", studioWalletapi.SetDefaultImportedAddress)

	// Add endpoint for wallet mnemonic retrieval
	wallet.POST("/retrieve-mnemonic",
		middleware.StudioPermissionRequired(rbac.ResourceAssetPoolPrivateKey, rbac.ActionExport),
		studioWalletapi.RetrieveOrganizationWalletMnemonic)
}

func registerStudioV2API(v1 *gin.RouterGroup) {
	studio := v1.Group("/studio")

	studio.POST("/login_v2", auth.AuthorizeByKgToken(), studiouser.LoginV2)
	studio.POST("/organization/:orgID/accept_invitation", auth.AuthorizeByKgToken(), studiouser.AcceptInvitation)

	studio.POST("/refresh", studiouser.RefreshToken)

	studio.GET("/organization/:orgID/customer_profile",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}),
		studioapi.GetCustomerProfile)

	studioAuthV2 := studio.Group("", middleware.AuthStudioTokenV2)
	studioAuthV2.GET("/organizations", organization.GetOrganizationsByActiveUser)
	studioAuthV2.GET("/asset_pro/transfer/tokens", assetproapi.Tokens)

	orgAuth := studioAuthV2.Group("/organization/:orgID", middleware.OrganizationUserValidation())

	// common
	orgAuth.GET("/ok", api.GetOk)
	orgAuth.GET("/info", organization.GetOrganizationInfo)
	orgAuth.PUT("/info",
		middleware.StudioPermissionRequired(rbac.ResourceOrganizationInfo, rbac.ActionEdit),
		organization.SaveOrganizationInfo)
	orgAuth.GET("/me", studiouser.UserInfo)
	orgAuth.GET("/accounts", organization.AccountV2)
	// customer
	orgAuth.GET("/customer", studioapi.GetCustomer)
	// role
	orgAuth.GET("/roles", studiouser.GetAllRoles)
	// user
	orgAuth.GET("/user_exist", studiouser.Exist)
	orgAuth.POST("/grafana_login",
		middleware.StudioPermissionRequiredOneOf([]rbac.Grant{
			{Resource: rbac.ResourceUser360StatisticsAssetPro, Action: rbac.ActionRead},
			{Resource: rbac.ResourceAssetProRevenue, Action: rbac.ActionRead},
		}),
		user360api.SignGrafanaAuthToken)

	// asset pro
	orgAuth.POST("/asset_pro/transfer",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionApply),
		assetproapi.TransferV2)
	orgAuth.GET("/asset_pro/transfer/filter_options",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionRead),
		assetproapi.HistoriesFilterOptions)
	orgAuth.GET("/asset_pro/transfer/histories",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionRead),
		assetproapi.Histories)
	orgAuth.GET("/asset_pro/transfer/histories/:id",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionRead),
		assetproapi.HistoryDetail)
	orgAuth.GET("/asset_pro/transfer/pending_history_count",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionRead),
		assetproapi.PendingHistoryCount)
	orgAuth.POST("/asset_pro/transfer/histories/:serial_id/approve",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionApprove),
		assetproapi.Approve)
	orgAuth.POST("/asset_pro/transfer/histories/:serial_id/release",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionRelease),
		assetproapi.Release)
	orgAuth.POST("/asset_pro/transfer/histories/:serial_id/resend",
		middleware.StudioPermissionRequiredOneOf([]rbac.Grant{
			{Resource: rbac.ResourceTransaction, Action: rbac.ActionApply},
			{Resource: rbac.ResourceTransaction, Action: rbac.ActionRelease}}),
		assetproapi.Resend)
	orgAuth.GET("/asset_pro/free_send_count",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionRead),
		assetproapi.GetFreeSendCount)
	orgAuth.GET("/asset_pro/operators",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOperator, rbac.ActionRead),
		assetproapi.ListOperators)
	orgAuth.PUT("/asset_pro/operators/:uid",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOperator, rbac.ActionEdit),
		assetproapi.UpdateOperator)
	orgAuth.GET("/asset_pro/market",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProMarketInfo, rbac.ActionRead),
		assetproapi.GetMarketInfoForMerchant)
	orgAuth.PUT("/asset_pro/market",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProMarketInfo, rbac.ActionEdit),
		assetproapi.SaveMarketInfoForMerchant)
	orgAuth.GET("/asset_pro/pending_order_count",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOrder, rbac.ActionRead),
		marketapi.CountOfPendingOrdersForMerchant)
	orgAuth.GET("/asset_pro/orders",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOrder, rbac.ActionRead),
		marketapi.ListOrdersForMerchant)
	orgAuth.GET("/asset_pro/orders/:order_id",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOrder, rbac.ActionRead),
		marketapi.GetOrderDetailForMerchant)
	orgAuth.POST("/asset_pro/orders/:order_id/transfer",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOrder, rbac.ActionEdit),
		marketapi.TransferProductForMerchant)
	orgAuth.PATCH("/asset_pro/orders/:order_id",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOrder, rbac.ActionEdit),
		marketapi.UpdateOrderForMerchant)
	orgAuth.GET("/asset_pro/liquidity",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProLiquidity, rbac.ActionRead),
		assetproapi.GetLiquidities)
	orgAuth.GET("/asset_pro/profit_rates",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProLiquidity, rbac.ActionRead),
		assetproapi.GetProfitRates)
	orgAuth.PUT("/asset_pro/profit_rates",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProLiquidity, rbac.ActionEdit),
		assetproapi.UpsertProfitRate)

	// asset pro - product
	orgAuth.GET("/asset_pro/products",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProProduct, rbac.ActionRead),
		marketapi.GetProductsForMerchant)
	orgAuth.PUT("/asset_pro/products/:product_id",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProProduct, rbac.ActionEdit),
		marketapi.UpdateProductForMerchant)
	orgAuth.POST("/asset_pro/orders/:order_id/confirm_payment",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOrder, rbac.ActionEdit),
		marketapi.ConfirmPaymentForMerchant)
	orgAuth.PUT("/asset_pro/orders/:order_id/cancel",
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOrder, rbac.ActionEdit),
		marketapi.CancelOrderForMerchant)

	// user360
	orgAuth.GET("/user_360/audience",
		middleware.StudioPermissionRequired(rbac.ResourceUser360StatisticsCompliance, rbac.ActionRead),
		user360api.ListAudience)
	orgAuth.GET("/user_360/audience/:uid",
		middleware.StudioPermissionRequired(rbac.ResourceUser360StatisticsCompliance, rbac.ActionRead),
		user360api.GetSingleAudience)
	orgAuth.GET("/user_360/compliance",
		middleware.StudioPermissionRequired(rbac.ResourceUser360StatisticsCompliance, rbac.ActionRead),
		user360api.StatisticsCompliance)
	// DEPRECATED: use /v1/studio/organization/:orgID/grafana_login instead
	orgAuth.POST("/user_360/asset_pro/grafana_login",
		middleware.StudioPermissionRequired(rbac.ResourceUser360StatisticsAssetPro, rbac.ActionRead),
		user360api.SignGrafanaAuthToken)

	// user
	orgAuth.POST("/users",
		middleware.StudioPermissionRequired(rbac.ResourceMember, rbac.ActionEdit),
		studiouser.InviteStudioUser)
	orgAuth.POST("/reinvite",
		middleware.StudioPermissionRequired(rbac.ResourceMember, rbac.ActionEdit),
		studiouser.ReinviteStudioUser)
	orgAuth.GET("/users",
		middleware.StudioPermissionRequired(rbac.ResourceMember, rbac.ActionRead),
		studiouser.ListUsers)
	orgAuth.PUT("/users/:uid",
		middleware.StudioPermissionRequired(rbac.ResourceMember, rbac.ActionEdit),
		studiouser.UpdateStudioUser)
	orgAuth.DELETE("/users/:userID",
		middleware.StudioPermissionRequired(rbac.ResourceMember, rbac.ActionEdit),
		studiouser.DisableUser)

	// application
	orgAuth.POST("/custom_auth_application",
		middleware.StudioPermissionRequired(rbac.ResourceApplication, rbac.ActionEdit),
		authapi.CreateCustomAuthApplication)

	// linebot
	lineGroup := orgAuth.Group("/line_bot")
	lineGroup.GET("/config", linebotapi.GetLinebotConfig)
	lineGroup.PUT("/config", linebotapi.UpsertLinebotConfig)
	lineGroup.GET("/exchange_rate", linebotapi.GetExchangeRates)
	lineGroup.PUT("/exchange_rate", linebotapi.UpsertExchangeRate)
	lineGroup.DELETE("/exchange_rate/:exchange_rate_id", linebotapi.DeleteExchangeRate)

	linePublicGroup := studio.Group("/line_bot")
	linePublicGroup.POST("/callback/:channel_id", linebotapi.Callback)
	linePublicGroup.GET("/link_redirect", linebotapi.LineAccountLinkRedirect)

	// compliance
	complyFlowGroup := orgAuth.Group("/comply_flow")
	complyFlowGroup.GET("/cases",
		middleware.StudioPermissionRequired(rbac.ResourceCaseSubmission, rbac.ActionRead),
		complianceapi.GetLatestCaseByUsers)
	complyFlowGroup.GET("/cases/filter_options",
		middleware.StudioPermissionRequired(rbac.ResourceCaseSubmission, rbac.ActionRead),
		complianceapi.GetCaseFilters)
	complyFlowGroup.GET("/cases/pending_count",
		middleware.StudioPermissionRequired(rbac.ResourceCaseSubmission, rbac.ActionRead),
		complianceapi.GetPendingCaseCount)
	complyFlowGroup.POST("/:uid/audit",
		middleware.StudioPermissionRequired(rbac.ResourceCaseSubmission, rbac.ActionEdit),
		complianceapi.Audit)
}

func registerStudioCustomerAPI(v1 *gin.RouterGroup) {
	v1.GET("/studio/asset_pro/market_config", assetproapi.GetMarketInfoForCustomer)

	studio := v1.Group("/studio/organization/:orgID")

	studio.GET("/orders",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeOrderRead}),
		middleware.OrganizationCustomerValidation(),
		middleware.OrganizationOauthClientValidation,
		marketapi.ListOrdersForCustomer)

	studio.GET("/orders/:order_id",
		auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeOrderRead}),
		middleware.OrganizationCustomerValidation(),
		middleware.OrganizationOauthClientValidation,
		marketapi.GetOrderDetailForCustomer)

	studio.POST("/orders",
		auth.AuthorizeByKgTokenOrOAuth([]string{oauth.ScopeOrderWrite}),
		middleware.KycValidation,
		middleware.OrganizationOauthClientValidation,
		marketapi.CreateOrdersForCustomer)

	studio.POST("/orders/:order_id/confirm_payment",
		auth.AuthorizeByKgTokenOrOAuth([]string{oauth.ScopeOrderWrite}),
		middleware.KycValidation,
		middleware.OrganizationOauthClientValidation,
		marketapi.ConfirmPaymentForCustomer)

	// everyone can get products
	studio.GET("/products",
		marketapi.GetProductsForCustomer)
}

// API Key authenticated endpoints
func registerAPIKeyAPIs(v1 *gin.RouterGroup) {
	externalAuth := v1.Group("/studio/api")
	externalAuth.Use(auth.ValidateStudioAPIKey)

	// wallet management
	externalAuth.GET("/wallet/getAddresses", user.GetAddresses)
	externalAuth.POST("/wallet/createUserWithMnemonic", user.CreateUserWithMnemonic)

	// payment intent - api key methods
	externalAuth.POST("/payment/intent", payment.CreatePaymentIntent)
	externalAuth.GET("/payment/intent/:id", payment.GetPaymentIntent)
	externalAuth.POST("/payment/intent/:intent_id/cancel", payment.CancelPaymentIntent)
	externalAuth.GET("/payment/intents", middleware.Pagination(), payment.GetPaymentIntents)
	externalAuth.GET("/payment/quote", payment.GetQuote)

	// asset pro
	externalAuth.POST("/asset_pro/transfer",
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionApply),
		assetproapi.TransferV2)
}
